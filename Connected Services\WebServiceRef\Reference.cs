﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//
//     对此文件的更改可能导致不正确的行为，并在以下条件下丢失:
//     代码重新生成。
// </auto-generated>
//------------------------------------------------------------------------------

namespace WebServiceRef
{
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://stockman.webservices.spring.web.ebiz.hdd.com/", ConfigurationName="WebServiceRef.QueryProductCodeService")]
    public interface QueryProductCodeService
    {
        
        // CODEGEN: 参数 "return" 需要其他架构信息，使用参数模式无法捕获这些信息。特定属性为“Microsoft.Xml.Serialization.XmlElementAttribute”。
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        [return: System.ServiceModel.MessageParameterAttribute(Name="return")]
        WebServiceRef.queryProductCodeResponse queryProductCode(WebServiceRef.queryProductCode request);
        
        [System.ServiceModel.OperationContractAttribute(Action="", ReplyAction="*")]
        System.Threading.Tasks.Task<WebServiceRef.queryProductCodeResponse> queryProductCodeAsync(WebServiceRef.queryProductCode request);
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://stockman.webservices.spring.web.ebiz.hdd.com/")]
    public partial class productCodeModelForWebService
    {
        
        private string productCodeField;
        
        private string productTitleField;
        
        private string productUnitField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=0)]
        public string productCode
        {
            get
            {
                return this.productCodeField;
            }
            set
            {
                this.productCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=1)]
        public string productTitle
        {
            get
            {
                return this.productTitleField;
            }
            set
            {
                this.productTitleField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified, Order=2)]
        public string productUnit
        {
            get
            {
                return this.productUnitField;
            }
            set
            {
                this.productUnitField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="queryProductCode", WrapperNamespace="http://stockman.webservices.spring.web.ebiz.hdd.com/", IsWrapped=true)]
    public partial class queryProductCode
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://stockman.webservices.spring.web.ebiz.hdd.com/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg0;
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://stockman.webservices.spring.web.ebiz.hdd.com/", Order=1)]
        [System.Xml.Serialization.XmlElementAttribute(Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public string arg1;
        
        public queryProductCode()
        {
        }
        
        public queryProductCode(string arg0, string arg1)
        {
            this.arg0 = arg0;
            this.arg1 = arg1;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
    [System.ServiceModel.MessageContractAttribute(WrapperName="queryProductCodeResponse", WrapperNamespace="http://stockman.webservices.spring.web.ebiz.hdd.com/", IsWrapped=true)]
    public partial class queryProductCodeResponse
    {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://stockman.webservices.spring.web.ebiz.hdd.com/", Order=0)]
        [System.Xml.Serialization.XmlElementAttribute("return", Form=System.Xml.Schema.XmlSchemaForm.Unqualified)]
        public WebServiceRef.productCodeModelForWebService[] @return;
        
        public queryProductCodeResponse()
        {
        }
        
        public queryProductCodeResponse(WebServiceRef.productCodeModelForWebService[] @return)
        {
            this.@return = @return;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public interface QueryProductCodeServiceChannel : WebServiceRef.QueryProductCodeService, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "2.2.0-preview1.23462.5")]
    public partial class QueryProductCodeServiceClient : System.ServiceModel.ClientBase<WebServiceRef.QueryProductCodeService>, WebServiceRef.QueryProductCodeService
    {
        
        /// <summary>
        /// 实现此分部方法，配置服务终结点。
        /// </summary>
        /// <param name="serviceEndpoint">要配置的终结点</param>
        /// <param name="clientCredentials">客户端凭据</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public QueryProductCodeServiceClient() : 
                base(QueryProductCodeServiceClient.GetDefaultBinding(), QueryProductCodeServiceClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.QueryProductCodeServiceImplPort.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public QueryProductCodeServiceClient(EndpointConfiguration endpointConfiguration) : 
                base(QueryProductCodeServiceClient.GetBindingForEndpoint(endpointConfiguration), QueryProductCodeServiceClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public QueryProductCodeServiceClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(QueryProductCodeServiceClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public QueryProductCodeServiceClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(QueryProductCodeServiceClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public QueryProductCodeServiceClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        WebServiceRef.queryProductCodeResponse WebServiceRef.QueryProductCodeService.queryProductCode(WebServiceRef.queryProductCode request)
        {
            return base.Channel.queryProductCode(request);
        }
        
        public WebServiceRef.productCodeModelForWebService[] queryProductCode(string arg0, string arg1)
        {
            WebServiceRef.queryProductCode inValue = new WebServiceRef.queryProductCode();
            inValue.arg0 = arg0;
            inValue.arg1 = arg1;
            WebServiceRef.queryProductCodeResponse retVal = ((WebServiceRef.QueryProductCodeService)(this)).queryProductCode(inValue);
            return retVal.@return;
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        System.Threading.Tasks.Task<WebServiceRef.queryProductCodeResponse> WebServiceRef.QueryProductCodeService.queryProductCodeAsync(WebServiceRef.queryProductCode request)
        {
            return base.Channel.queryProductCodeAsync(request);
        }
        
        public System.Threading.Tasks.Task<WebServiceRef.queryProductCodeResponse> queryProductCodeAsync(string arg0, string arg1)
        {
            WebServiceRef.queryProductCode inValue = new WebServiceRef.queryProductCode();
            inValue.arg0 = arg0;
            inValue.arg1 = arg1;
            return ((WebServiceRef.QueryProductCodeService)(this)).queryProductCodeAsync(inValue);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.QueryProductCodeServiceImplPort))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("找不到名称为“{0}”的终结点。", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.QueryProductCodeServiceImplPort))
            {
                return new System.ServiceModel.EndpointAddress("http://************/stockman/webservices/queryProductCodeService");
            }
            throw new System.InvalidOperationException(string.Format("找不到名称为“{0}”的终结点。", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return QueryProductCodeServiceClient.GetBindingForEndpoint(EndpointConfiguration.QueryProductCodeServiceImplPort);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return QueryProductCodeServiceClient.GetEndpointAddress(EndpointConfiguration.QueryProductCodeServiceImplPort);
        }
        
        public enum EndpointConfiguration
        {
            
            QueryProductCodeServiceImplPort,
        }
    }
}
