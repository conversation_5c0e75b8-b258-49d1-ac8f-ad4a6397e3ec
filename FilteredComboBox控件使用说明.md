# FilteredComboBox 控件使用说明

## 概述

FilteredComboBox 是一个支持文本输入过滤的 ComboBox 控件，已经从 MainWindow.xaml.cs 中独立出来，放置在 `controls` 目录下，可以在项目中复用。

## 文件结构

```
wpfprinterapp/
├── controls/
│   ├── FilteredComboBox.xaml          # 控件的 XAML 定义
│   └── FilteredComboBox.xaml.cs       # 控件的逻辑实现
├── MainWindow.xaml                    # 主窗口（使用了 FilteredComboBox）
├── MainWindow.xaml.cs                 # 主窗口逻辑
└── ...
```

## 功能特性

### 1. 文本过滤
- **实时过滤**: 用户输入文本时，下拉列表会实时过滤显示匹配的项目
- **不区分大小写**: 过滤时忽略大小写
- **包含匹配**: 支持部分文本匹配，不需要完全匹配

### 2. 键盘操作
- **回车键**: 确认选择当前项目或自动选择匹配项
- **Escape键**: 清除过滤条件并关闭下拉框
- **删除键**: 支持 Backspace 和 Delete 键删除文本并更新过滤

### 3. 自动验证
- **失去焦点验证**: 当控件失去焦点时，自动验证输入的文本是否在列表中
- **自动清理**: 如果输入的文本不在列表中，自动清空文本框

### 4. 下拉框管理
- **智能打开**: 有过滤条件时自动打开下拉框
- **智能关闭**: 选择项目后自动关闭下拉框

## 使用方法

### 1. 在 XAML 中使用

首先在 Window 或 UserControl 中添加命名空间引用：

```xml
<Window x:Class="YourApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:wpfprinterapp.Controls"
        ...>
```

然后使用控件：

```xml
<controls:FilteredComboBox x:Name="MyFilteredComboBox"
                          ItemsSource="{Binding MyDataSource}"
                          SelectedItem="{Binding SelectedItem}"
                          SelectionChanged="MyFilteredComboBox_SelectionChanged"
                          Style="{StaticResource YourComboBoxStyle}"
                          Margin="10" />
```

### 2. 在代码中使用

```csharp
// 创建控件实例
var filteredComboBox = new FilteredComboBox();

// 设置数据源
filteredComboBox.ItemsSource = new[] { "选项1", "选项2", "选项3" };

// 订阅事件
filteredComboBox.SelectionChanged += (sender, e) =>
{
    // 处理选择变化
    var selectedItem = filteredComboBox.SelectedItem;
};

// 添加到界面
myPanel.Children.Add(filteredComboBox);
```

### 3. 数据绑定

```csharp
// 在 ViewModel 中
public ObservableCollection<string> Items { get; set; }
public string SelectedItem { get; set; }

// 在 XAML 中
<controls:FilteredComboBox ItemsSource="{Binding Items}"
                          SelectedItem="{Binding SelectedItem, Mode=TwoWay}" />
```

## 属性和事件

### 继承的属性
FilteredComboBox 继承自 ComboBox，因此支持所有 ComboBox 的属性：

- `ItemsSource`: 数据源
- `SelectedItem`: 选中项
- `SelectedIndex`: 选中索引
- `Text`: 文本内容
- `IsDropDownOpen`: 下拉框是否打开

### 继承的事件
- `SelectionChanged`: 选择变化事件
- `TextChanged`: 文本变化事件
- `DropDownOpened`: 下拉框打开事件
- `DropDownClosed`: 下拉框关闭事件

## 样式定制

FilteredComboBox 可以使用标准的 ComboBox 样式：

```xml
<Style x:Key="MyFilteredComboBoxStyle" TargetType="{x:Type controls:FilteredComboBox}">
    <Setter Property="Background" Value="White"/>
    <Setter Property="BorderBrush" Value="Gray"/>
    <Setter Property="BorderThickness" Value="1"/>
    <Setter Property="Padding" Value="5"/>
    <!-- 其他样式设置 -->
</Style>
```

## 在其他项目中复用

### 1. 复制文件
将 `controls` 目录复制到目标项目中。

### 2. 修改命名空间
如果目标项目的命名空间不同，需要修改：

在 `FilteredComboBox.xaml.cs` 中：
```csharp
namespace YourProject.Controls  // 修改为目标项目的命名空间
{
    public partial class FilteredComboBox : ComboBox
    {
        // ...
    }
}
```

在使用的 XAML 文件中：
```xml
xmlns:controls="clr-namespace:YourProject.Controls"
```

### 3. 添加依赖
确保目标项目包含必要的引用：
- System.Windows.Controls
- System.Windows.Data

## 调试和故障排除

### 1. 启用调试输出
控件内部包含详细的调试输出，可以在 Visual Studio 的输出窗口中查看：

```
FilterItem: 'e06 - 女式牛仔裤' contains 'e06' = True
ApplyFilter: Applying filter 'e06'
```

### 2. 常见问题

**问题**: 过滤不工作
**解决**: 确保 ItemsSource 已正确设置，且数据项的 ToString() 方法返回有意义的文本

**问题**: 选择事件不触发
**解决**: 检查是否正确订阅了 SelectionChanged 事件

**问题**: 样式不生效
**解决**: 确保样式的 TargetType 设置为 FilteredComboBox 类型

## 扩展功能

### 可以添加的功能
1. **自定义过滤逻辑**: 允许用户自定义过滤条件
2. **高亮匹配文本**: 在下拉列表中高亮显示匹配的文本
3. **最近使用项**: 记住最近选择的项目
4. **异步数据加载**: 支持大数据集的异步加载和过滤

### 示例扩展
```csharp
// 自定义过滤委托
public delegate bool FilterDelegate(object item, string filter);

public FilterDelegate CustomFilter { get; set; }

// 在 FilterItem 方法中使用
private bool FilterItem(object value)
{
    if (CustomFilter != null)
    {
        return CustomFilter(value, currentFilter);
    }
    
    // 默认过滤逻辑
    // ...
}
```

## 版本历史

- **v1.0**: 初始版本，从 MainWindow 中独立出来
- **v1.1**: 简化实现，提高稳定性
- **v1.2**: 添加完整的文档和使用说明

---

**开发团队**: WPF打印应用程序项目组  
**最后更新**: 2025年1月  
**版本**: v1.2
