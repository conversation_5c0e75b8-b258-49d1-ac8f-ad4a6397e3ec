using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;

namespace wpfprinterapp.Controls
{
    /// <summary>
    /// FilteredComboBox 使用示例窗口
    /// </summary>
    public partial class FilteredComboBoxExample : Window, INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        // 数据绑定属性
        public ObservableCollection<string> Colors { get; set; }
        
        private string? _selectedColor;
        public string? SelectedColor
        {
            get => _selectedColor;
            set
            {
                _selectedColor = value;
                OnPropertyChanged(nameof(SelectedColor));
                UpdateResult($"数据绑定选择: {value}");
            }
        }

        public FilteredComboBoxExample()
        {
            InitializeComponent();
            InitializeData();
            DataContext = this;
        }

        private void InitializeData()
        {
            // 示例1：编程语言
            var languages = new[]
            {
                "C#", "Java", "Python", "JavaScript", "TypeScript",
                "C++", "C", "Go", "Rust", "Swift",
                "<PERSON><PERSON><PERSON>", "Scala", "F#", "VB.NET", "<PERSON><PERSON>",
                "Ruby", "Perl", "Lua", "R", "MATLAB"
            };
            LanguageComboBox.ItemsSource = languages;

            // 示例2：城市
            var cities = new[]
            {
                "北京", "上海", "广州", "深圳", "杭州",
                "南京", "苏州", "成都", "重庆", "武汉",
                "西安", "天津", "青岛", "大连", "厦门",
                "长沙", "郑州", "济南", "福州", "昆明"
            };
            CityComboBox.ItemsSource = cities;

            // 示例3：颜色（用于数据绑定）
            Colors = new ObservableCollection<string>
            {
                "红色", "橙色", "黄色", "绿色", "青色",
                "蓝色", "紫色", "粉色", "棕色", "黑色",
                "白色", "灰色", "银色", "金色", "玫瑰金"
            };
        }

        private void LanguageComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is FilteredComboBox comboBox && comboBox.SelectedItem != null)
            {
                UpdateResult($"编程语言选择: {comboBox.SelectedItem}");
            }
        }

        private void CityComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is FilteredComboBox comboBox && comboBox.SelectedItem != null)
            {
                UpdateResult($"城市选择: {comboBox.SelectedItem}");
            }
        }

        private void ColorComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 这个事件处理器是可选的，因为我们使用了数据绑定
            // SelectedColor 属性的 setter 会自动处理更新
        }

        private void UpdateResult(string message)
        {
            if (ResultTextBlock != null)
            {
                ResultTextBlock.Text = $"{System.DateTime.Now:HH:mm:ss} - {message}";
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
