<Window x:Class="wpfprinterapp.Controls.FilteredComboBoxExample"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:wpfprinterapp.Controls"
        Title="FilteredComboBox 使用示例" Height="400" Width="600"
        WindowStartupLocation="CenterScreen">
    
    <Window.Resources>
        <!-- 自定义样式 -->
        <Style x:Key="ExampleComboBoxStyle" TargetType="{x:Type controls:FilteredComboBox}">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#3498db"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="35"/>
        </Style>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="FilteredComboBox 控件使用示例" 
                   FontSize="18" FontWeight="Bold" HorizontalAlignment="Center"/>
        
        <!-- 示例1：基本使用 -->
        <StackPanel Grid.Row="2">
            <Label Content="示例1：基本使用 - 选择编程语言" FontWeight="Bold"/>
            <controls:FilteredComboBox x:Name="LanguageComboBox"
                                      Style="{StaticResource ExampleComboBoxStyle}"
                                      SelectionChanged="LanguageComboBox_SelectionChanged"/>
        </StackPanel>
        
        <!-- 示例2：自定义数据 -->
        <StackPanel Grid.Row="4">
            <Label Content="示例2：自定义数据 - 选择城市" FontWeight="Bold"/>
            <controls:FilteredComboBox x:Name="CityComboBox"
                                      Style="{StaticResource ExampleComboBoxStyle}"
                                      SelectionChanged="CityComboBox_SelectionChanged"/>
        </StackPanel>
        
        <!-- 示例3：数据绑定 -->
        <StackPanel Grid.Row="6">
            <Label Content="示例3：数据绑定 - 选择颜色" FontWeight="Bold"/>
            <controls:FilteredComboBox x:Name="ColorComboBox"
                                      Style="{StaticResource ExampleComboBoxStyle}"
                                      ItemsSource="{Binding Colors}"
                                      SelectedItem="{Binding SelectedColor, Mode=TwoWay}"
                                      SelectionChanged="ColorComboBox_SelectionChanged"/>
        </StackPanel>
        
        <!-- 选择结果显示 -->
        <StackPanel Grid.Row="8">
            <Label Content="选择结果：" FontWeight="Bold"/>
            <TextBlock x:Name="ResultTextBlock" 
                       Text="请在上面的下拉框中选择项目..."
                       Background="LightGray" 
                       Padding="10"
                       TextWrapping="Wrap"/>
        </StackPanel>
        
        <!-- 使用说明 -->
        <ScrollViewer Grid.Row="9" Margin="0,20,0,0">
            <TextBlock TextWrapping="Wrap" FontSize="12" Foreground="Gray">
                <Run Text="使用说明："/>
                <LineBreak/>
                <Run Text="1. 在下拉框中输入文本可以实时过滤选项"/>
                <LineBreak/>
                <Run Text="2. 按回车键确认选择"/>
                <LineBreak/>
                <Run Text="3. 按Escape键清除过滤条件"/>
                <LineBreak/>
                <Run Text="4. 支持键盘导航（上下箭头键）"/>
                <LineBreak/>
                <Run Text="5. 失去焦点时自动验证输入"/>
            </TextBlock>
        </ScrollViewer>
    </Grid>
</Window>
