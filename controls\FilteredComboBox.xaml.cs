using System;
using System.Collections;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;

namespace wpfprinterapp.Controls
{
    /// <summary>
    /// 支持文本输入过滤的 ComboBox 控件
    /// </summary>
    public partial class FilteredComboBox : ComboBox
    {
        private string currentFilter = string.Empty;

        public FilteredComboBox()
        {
            InitializeComponent();
            IsEditable = true;
            IsTextSearchEnabled = false;
            StaysOpenOnEdit = true;

            // 订阅事件
            Loaded += FilteredComboBox_Loaded;
        }

        private void FilteredComboBox_Loaded(object sender, RoutedEventArgs e)
        {
            // 在控件加载后订阅 PreviewTextInput 事件
            PreviewTextInput += FilteredComboBox_PreviewTextInput;
        }

        private void FilteredComboBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // 获取即将输入后的文本
            var currentText = Text ?? "";
            var newText = currentText + e.Text;

            // 应用过滤
            if (newText != currentFilter)
            {
                currentFilter = newText;
                ApplyFilter();

                // 确保下拉框打开
                if (!string.IsNullOrEmpty(newText))
                {
                    IsDropDownOpen = true;
                }
            }
        }

        protected override void OnItemsSourceChanged(IEnumerable oldValue, IEnumerable newValue)
        {
            if (newValue != null)
            {
                var view = CollectionViewSource.GetDefaultView(newValue);
                if (view != null)
                {
                    view.Filter = FilterItem;
                }
            }

            if (oldValue != null)
            {
                var view = CollectionViewSource.GetDefaultView(oldValue);
                if (view != null) 
                {
                    view.Filter -= FilterItem;
                }
            }

            base.OnItemsSourceChanged(oldValue, newValue);
        }

        protected override void OnDropDownOpened(EventArgs e)
        {
            // 如果没有过滤条件，显示所有项目
            if (string.IsNullOrEmpty(currentFilter))
            {
                ClearFilter();
            }

            base.OnDropDownOpened(e);
        }

        protected override void OnPreviewKeyDown(KeyEventArgs e)
        {
            // 处理回车键
            if (e.Key == Key.Enter)
            {
                if (SelectedItem != null)
                {
                    // 有选中项，确认选择
                    ClearFilter();
                    IsDropDownOpen = false;
                    e.Handled = true;
                    return;
                }
                else if (!string.IsNullOrEmpty(currentFilter))
                {
                    // 没有选中项但有过滤文本，尝试找到匹配项
                    foreach (var item in Items)
                    {
                        if (item.ToString()?.Equals(currentFilter, StringComparison.OrdinalIgnoreCase) == true)
                        {
                            SelectedItem = item;
                            ClearFilter();
                            IsDropDownOpen = false;
                            e.Handled = true;
                            return;
                        }
                    }
                }
            }

            if (e.Key == Key.Escape)
            {
                ClearFilter();
                Text = "";
                IsDropDownOpen = false;
                e.Handled = true;
                return;
            }

            if (e.Key == Key.Back || e.Key == Key.Delete)
            {
                // 处理删除键
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    var newText = Text ?? "";
                    if (newText != currentFilter)
                    {
                        currentFilter = newText;
                        ApplyFilter();

                        if (!string.IsNullOrEmpty(newText))
                        {
                            IsDropDownOpen = true;
                        }
                    }
                }), System.Windows.Threading.DispatcherPriority.Input);
            }

            base.OnPreviewKeyDown(e);
        }

        protected override void OnPreviewLostKeyboardFocus(KeyboardFocusChangedEventArgs e)
        {
            ClearFilter();

            // 验证输入的文本是否在列表中
            if (!string.IsNullOrEmpty(Text))
            {
                bool found = false;
                foreach (var item in Items)
                {
                    if (item.ToString()?.Equals(Text, StringComparison.OrdinalIgnoreCase) == true)
                    {
                        SelectedItem = item;
                        found = true;
                        break;
                    }
                }

                // 如果没有找到匹配项，清空文本
                if (!found)
                {
                    Text = string.Empty;
                    SelectedItem = null;
                }
            }

            base.OnPreviewLostKeyboardFocus(e);
        }

        private void ApplyFilter()
        {
            if (ItemsSource == null)
            {
                return;
            }

            var view = CollectionViewSource.GetDefaultView(ItemsSource);
            view?.Refresh();

            if (!string.IsNullOrEmpty(currentFilter))
            {
                IsDropDownOpen = true;
            }
        }

        private void ClearFilter()
        {
            currentFilter = string.Empty;
            if (ItemsSource != null)
            {
                var view = CollectionViewSource.GetDefaultView(ItemsSource);
                view?.Refresh();
            }
        }

        private bool FilterItem(object value)
        {
            if (value == null)
            {
                return false;
            }

            if (string.IsNullOrEmpty(currentFilter))
            {
                return true;
            }

            var itemText = value.ToString();
            return itemText?.Contains(currentFilter, StringComparison.OrdinalIgnoreCase) == true;
        }
    }
}
