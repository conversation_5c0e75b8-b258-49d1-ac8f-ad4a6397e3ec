﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using QRCoder;

namespace wpfprinterapp
{
    /// <summary>
    /// 智能标签打印系统主窗口
    /// </summary>
    public partial class MainWindow : Window
    {
        // 产品数据映射
        private readonly Dictionary<string, ProductInfo> _productMap = new()
        {
            { "e06 - 女式牛仔裤", new ProductInfo("e06", "女式牛仔裤", "件") },
            { "e10 - 男士T恤", new ProductInfo("e10", "男士T恤", "件") },
            { "b12 - 运动鞋", new ProductInfo("e12", "运动鞋", "双") },
            { "e15 - 西装套装", new ProductInfo("e15", "西装套装", "套") }
        };

        // 尺码数据
        private readonly Dictionary<string, string[]> _sizeOptions = new()
        {
            { "char", new[] { "S", "M", "L", "XL", "XXL", "XXXL" } },
            { "num", new[] { "110", "120", "130", "140", "150", "160", "170", "180" } }
        };

        // 颜色选项
        private readonly string[] _colorOptions = { "黑色", "白色", "牛仔蓝", "卡其色", "红色", "绿色", "灰色" };

        private string _lastValidProduct = "e06 - 女式牛仔裤";

        public MainWindow()
        {
            InitializeComponent();
            InitializeData();
            BindEvents();
            UpdatePreview();
        }

        /// <summary>
        /// 绑定事件处理器
        /// </summary>
        private void BindEvents()
        {
            // 绑定事件处理器
            ProductCodeComboBox.SelectionChanged += ProductCodeComboBox_SelectionChanged;
            ColorComboBox.SelectionChanged += ColorComboBox_SelectionChanged;
            SizeComboBox.SelectionChanged += SizeComboBox_SelectionChanged;
            QuantityTextBox.TextChanged += QuantityTextBox_TextChanged;
            CharSizeRadio.Checked += SizeTypeRadio_Checked;
            NumSizeRadio.Checked += SizeTypeRadio_Checked;
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 初始化产品编号下拉框 - 使用 ItemsSource 而不是 Items.Add
            ProductCodeComboBox.ItemsSource = _productMap.Keys.ToList();
            ProductCodeComboBox.SelectedItem = _lastValidProduct;

            // 初始化颜色下拉框
            foreach (var color in _colorOptions)
            {
                ColorComboBox.Items.Add(color);
            }
            ColorComboBox.SelectedItem = "牛仔蓝";

            // 初始化尺码
            UpdateSizeOptions();
        }

        /// <summary>
        /// 更新尺码选项
        /// </summary>
        private void UpdateSizeOptions()
        {
            // 检查控件是否已初始化
            if (SizeComboBox == null || CharSizeRadio == null)
                return;

            var sizeType = CharSizeRadio.IsChecked == true ? "char" : "num";
            var currentSize = SizeComboBox.SelectedItem?.ToString();

            SizeComboBox.Items.Clear();
            foreach (var size in _sizeOptions[sizeType])
            {
                SizeComboBox.Items.Add(size);
            }

            // 保持当前选择或选择第一个
            if (!string.IsNullOrEmpty(currentSize) && _sizeOptions[sizeType].Contains(currentSize))
            {
                SizeComboBox.SelectedItem = currentSize;
            }
            else
            {
                SizeComboBox.SelectedIndex = 0;
            }

            UpdatePreview();
        }

        /// <summary>
        /// 更新标签预览
        /// </summary>
        private void UpdatePreview()
        {
            try
            {
                // 检查所有必要的控件是否已初始化
                if (ProductCodeComboBox == null || ColorComboBox == null ||
                    SizeComboBox == null || QuantityTextBox == null ||
                    UnitTextBox == null || LabelSkuTextBlock == null ||
                    LabelQuantityTextBlock == null || QrDataTextBlock == null ||
                    QrCodeImage == null)
                {
                    return;
                }

                var productDisplayValue = ProductCodeComboBox.Text ?? "";
                if (string.IsNullOrEmpty(productDisplayValue) || !_productMap.ContainsKey(productDisplayValue))
                {
                    return; // 无效产品时不更新预览
                }

                var productInfo = _productMap[productDisplayValue];
                var color = ColorComboBox.Text ?? "";
                var size = SizeComboBox.SelectedItem?.ToString() ?? "";
                var quantity = QuantityTextBox.Text ?? "0";

                // 更新单位
                UnitTextBox.Text = productInfo.Unit;

                // 更新SKU显示
                LabelSkuTextBlock.Text = $"{productInfo.Code}-{productInfo.Name}-{color}-{size}";

                // 更新数量显示
                LabelQuantityTextBlock.Text = $"{quantity} {productInfo.Unit}";

                // 更新二维码数据和图片
                var qrData = $"{productInfo.Code}-{color.ToLower().Replace(" ", "")}-{size}-{quantity}-{productInfo.Unit.ToLower()}";
                QrDataTextBlock.Text = qrData;

                // 生成二维码图片
                GenerateQrCode(qrData);
            }
            catch (Exception ex)
            {
                // 处理异常，可以记录日志或显示错误信息
                System.Diagnostics.Debug.WriteLine($"更新预览时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成二维码图片
        /// </summary>
        /// <param name="data">二维码数据</param>
        private void GenerateQrCode(string data)
        {
            try
            {
                using (var qrGenerator = new QRCodeGenerator())
                {
                    var qrCodeData = qrGenerator.CreateQrCode(data, QRCodeGenerator.ECCLevel.Q);
                    using (var qrCode = new QRCode(qrCodeData))
                    {
                        using (var qrCodeBitmap = qrCode.GetGraphic(20))
                        {
                            // 将Bitmap转换为BitmapImage
                            var bitmapImage = ConvertBitmapToBitmapImage(qrCodeBitmap);
                            QrCodeImage.Source = bitmapImage;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"生成二维码时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 将System.Drawing.Bitmap转换为WPF的BitmapImage
        /// </summary>
        /// <param name="bitmap">要转换的Bitmap</param>
        /// <returns>转换后的BitmapImage</returns>
        private BitmapImage ConvertBitmapToBitmapImage(Bitmap bitmap)
        {
            using (var memory = new MemoryStream())
            {
                bitmap.Save(memory, System.Drawing.Imaging.ImageFormat.Png);
                memory.Position = 0;

                var bitmapImage = new BitmapImage();
                bitmapImage.BeginInit();
                bitmapImage.StreamSource = memory;
                bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                bitmapImage.EndInit();
                bitmapImage.Freeze();

                return bitmapImage;
            }
        }

        // 事件处理器
        private void ProductCodeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ProductCodeComboBox.SelectedItem != null)
            {
                _lastValidProduct = ProductCodeComboBox.SelectedItem.ToString()!;
                UpdatePreview();
            }
        }



        private void ColorComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void SizeTypeRadio_Checked(object sender, RoutedEventArgs e)
        {
            UpdateSizeOptions();
        }

        private void SizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void QuantityTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printCount = int.Parse(PrintCountTextBox.Text);
                var productName = ProductCodeComboBox.Text;
                var color = ColorComboBox.Text;
                var size = SizeComboBox.SelectedItem?.ToString();
                var quantity = QuantityTextBox.Text;
                var unit = UnitTextBox.Text;

                var message = $"准备打印 {printCount} 张标签:\n" +
                             $"产品: {productName}\n" +
                             $"颜色: {color}\n" +
                             $"尺码: {size}\n" +
                             $"数量: {quantity} {unit}";

                MessageBox.Show(message, "打印确认", MessageBoxButton.OK, MessageBoxImage.Information);

                // TODO: 在这里实现实际的打印逻辑
                // 可以调用打印机API或生成打印文件
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打印时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    /// <summary>
    /// 产品信息类
    /// </summary>
    public class ProductInfo
    {
        public string Code { get; }
        public string Name { get; }
        public string Unit { get; }

        public ProductInfo(string code, string name, string unit)
        {
            Code = code;
            Name = name;
            Unit = unit;
        }
    }

    /// <summary>
    /// 支持文本输入过滤的 ComboBox
    /// </summary>
    public class FilteredComboBox : ComboBox
    {
        private string currentFilter = string.Empty;
        private bool isUpdatingText = false;

        public FilteredComboBox()
        {
            IsEditable = true;
            IsTextSearchEnabled = false;
            StaysOpenOnEdit = true;

            // 直接订阅事件
            Loaded += FilteredComboBox_Loaded;
        }

        private void FilteredComboBox_Loaded(object sender, RoutedEventArgs e)
        {
            // 在控件加载后订阅 PreviewTextInput 事件
            PreviewTextInput += FilteredComboBox_PreviewTextInput;
        }

        private void FilteredComboBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // 获取即将输入后的文本
            var currentText = Text ?? "";
            var newText = currentText + e.Text;

            System.Diagnostics.Debug.WriteLine($"PreviewTextInput: Current='{currentText}', Adding='{e.Text}', New='{newText}'");

            // 应用过滤
            if (newText != currentFilter)
            {
                currentFilter = newText;
                System.Diagnostics.Debug.WriteLine($"Setting currentFilter to: '{currentFilter}'");
                ApplyFilter();

                // 确保下拉框打开
                if (!string.IsNullOrEmpty(newText))
                {
                    IsDropDownOpen = true;
                    System.Diagnostics.Debug.WriteLine("Opening dropdown");
                }
            }
        }

        protected override void OnItemsSourceChanged(IEnumerable oldValue, IEnumerable newValue)
        {
            if (newValue != null)
            {
                var view = CollectionViewSource.GetDefaultView(newValue);
                view.Filter += FilterItem;
            }

            if (oldValue != null)
            {
                var view = CollectionViewSource.GetDefaultView(oldValue);
                if (view != null) view.Filter -= FilterItem;
            }

            base.OnItemsSourceChanged(oldValue, newValue);
        }

        protected override void OnSelectionChanged(SelectionChangedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"OnSelectionChanged: SelectedItem={SelectedItem}");

            // 只有在用户真正选择了项目时才清除过滤
            // if (SelectedItem != null && IsDropDownOpen)
            // {
            //     System.Diagnostics.Debug.WriteLine("OnSelectionChanged: Item selected, clearing filter");
            //     // 延迟清除过滤，让选择完成
            //     Dispatcher.BeginInvoke(new Action(() =>
            //     {
            //         ClearFilter();
            //     }), System.Windows.Threading.DispatcherPriority.Background);
            // }

            base.OnSelectionChanged(e);
        }

        protected override void OnDropDownOpened(EventArgs e)
        {
            // 只有在没有过滤条件时才清除过滤
            System.Diagnostics.Debug.WriteLine($"OnDropDownOpened: currentFilter='{currentFilter}'");

            // 如果没有过滤条件，显示所有项目
            if (string.IsNullOrEmpty(currentFilter))
            {
                System.Diagnostics.Debug.WriteLine("OnDropDownOpened: No filter, clearing to show all items");
                ClearFilter();
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("OnDropDownOpened: Keeping current filter");
            }

            base.OnDropDownOpened(e);
        }

        protected override void OnPreviewKeyDown(KeyEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"OnPreviewKeyDown: Key={e.Key}, IsDropDownOpen={IsDropDownOpen}, SelectedIndex={SelectedIndex}");

            // 处理向下箭头键
            if (e.Key == Key.Down)
            {
                if (!IsDropDownOpen)
                {
                    System.Diagnostics.Debug.WriteLine("OnPreviewKeyDown: Opening dropdown with Down key");
                    IsDropDownOpen = true;
                    e.Handled = true;
                    return;
                }
                else
                {
                    // 如果下拉框已经打开，处理导航
                    System.Diagnostics.Debug.WriteLine($"OnPreviewKeyDown: Down key navigation, current SelectedIndex={SelectedIndex}");

                    // 如果没有选中项，选中第一个可见项
                    if (SelectedIndex == -1)
                    {
                        // 查找第一个可见的项目
                        for (int i = 0; i < Items.Count; i++)
                        {
                            var item = Items[i];
                            if (FilterItem(item))
                            {
                                SelectedIndex = i;
                                System.Diagnostics.Debug.WriteLine($"OnPreviewKeyDown: Selected first visible item at index {i}: {item}");
                                e.Handled = true;
                                return;
                            }
                        }
                    }
                    else
                    {
                        // 已有选中项，选择下一个可见项
                        for (int i = SelectedIndex + 1; i < Items.Count; i++)
                        {
                            var item = Items[i];
                            if (FilterItem(item))
                            {
                                SelectedIndex = i;
                                System.Diagnostics.Debug.WriteLine($"OnPreviewKeyDown: Selected next visible item at index {i}: {item}");
                                e.Handled = true;
                                return;
                            }
                        }
                    }

                    // 如果没有找到合适的项目，让默认处理
                    System.Diagnostics.Debug.WriteLine("OnPreviewKeyDown: No suitable item found, using default navigation");
                    base.OnPreviewKeyDown(e);
                    return;
                }
            }

            // 处理向上箭头键
            if (e.Key == Key.Up && IsDropDownOpen)
            {
                System.Diagnostics.Debug.WriteLine($"OnPreviewKeyDown: Up key navigation, current SelectedIndex={SelectedIndex}");

                if (SelectedIndex > 0)
                {
                    // 查找上一个可见项
                    for (int i = SelectedIndex - 1; i >= 0; i--)
                    {
                        var item = Items[i];
                        if (FilterItem(item))
                        {
                            SelectedIndex = i;
                            System.Diagnostics.Debug.WriteLine($"OnPreviewKeyDown: Selected previous visible item at index {i}: {item}");
                            e.Handled = true;
                            return;
                        }
                    }
                }

                // 如果没有找到合适的项目，让默认处理
                System.Diagnostics.Debug.WriteLine("OnPreviewKeyDown: No suitable previous item found, using default navigation");
                base.OnPreviewKeyDown(e);
                return;
            }

            // 处理回车键
            if (e.Key == Key.Enter)
            {
                System.Diagnostics.Debug.WriteLine($"OnPreviewKeyDown: Enter pressed, SelectedItem={SelectedItem}");
                if (SelectedItem != null)
                {
                    // 有选中项，确认选择
                    System.Diagnostics.Debug.WriteLine("OnPreviewKeyDown: Confirming selection and updating preview");
                    ClearFilter();
                    IsDropDownOpen = false;

                    // 手动触发选择变化事件来更新预览
                    var args = new SelectionChangedEventArgs(SelectionChangedEvent, Array.Empty<object>(), new object[] { SelectedItem });
                    RaiseEvent(args);

                    e.Handled = true;
                    return;
                }
                else if (!string.IsNullOrEmpty(currentFilter))
                {
                    // 没有选中项但有过滤文本，尝试找到匹配项
                    foreach (var item in Items)
                    {
                        if (item.ToString().Equals(currentFilter, StringComparison.OrdinalIgnoreCase))
                        {
                            SelectedItem = item;
                            System.Diagnostics.Debug.WriteLine($"OnPreviewKeyDown: Auto-selected matching item: {item}");
                            ClearFilter();
                            IsDropDownOpen = false;

                            // 手动触发选择变化事件来更新预览
                            var args = new SelectionChangedEventArgs(SelectionChangedEvent, Array.Empty<object>(), new object[] { SelectedItem });
                            RaiseEvent(args);

                            e.Handled = true;
                            return;
                        }
                    }
                }
            }

            if (e.Key == Key.Escape)
            {
                ClearFilter();
                Text = "";
                IsDropDownOpen = false;
                e.Handled = true;
                return;
            }

            if (e.Key == Key.Back || e.Key == Key.Delete)
            {
                // 处理删除键
                System.Diagnostics.Debug.WriteLine($"OnPreviewKeyDown: {e.Key} pressed");
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    var newText = Text ?? "";
                    System.Diagnostics.Debug.WriteLine($"OnPreviewKeyDown Dispatcher: Text after {e.Key} = '{newText}'");
                    if (newText != currentFilter)
                    {
                        currentFilter = newText;
                        ApplyFilter();

                        if (!string.IsNullOrEmpty(newText))
                        {
                            IsDropDownOpen = true;
                        }
                    }
                }), System.Windows.Threading.DispatcherPriority.Input);
            }

            base.OnPreviewKeyDown(e);
        }

        private void ApplyFilter()
        {
            if (ItemsSource == null)
            {
                System.Diagnostics.Debug.WriteLine("ApplyFilter: ItemsSource is null");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"ApplyFilter: Applying filter '{currentFilter}' to {((System.Collections.IList)ItemsSource).Count} items");
            var view = CollectionViewSource.GetDefaultView(ItemsSource);
            view.Refresh();

            if (!string.IsNullOrEmpty(currentFilter))
            {
                IsDropDownOpen = true;
                System.Diagnostics.Debug.WriteLine("ApplyFilter: Opening dropdown");
            }
        }

        protected override void OnPreviewLostKeyboardFocus(KeyboardFocusChangedEventArgs e)
        {
            ClearFilter();

            // 验证输入的文本是否在列表中
            if (!string.IsNullOrEmpty(Text))
            {
                bool found = false;
                foreach (var item in Items)
                {
                    if (item.ToString().Equals(Text, StringComparison.OrdinalIgnoreCase))
                    {
                        SelectedItem = item;
                        found = true;
                        break;
                    }
                }

                // 如果没有找到匹配项，清空文本
                if (!found)
                {
                    Text = string.Empty;
                    SelectedItem = null;
                }
            }

            base.OnPreviewLostKeyboardFocus(e);
        }

        private void ClearFilter()
        {
            System.Diagnostics.Debug.WriteLine($"ClearFilter: Clearing filter (was '{currentFilter}')");
            currentFilter = string.Empty;
            if (ItemsSource != null)
            {
                var view = CollectionViewSource.GetDefaultView(ItemsSource);
                view.Refresh();
                System.Diagnostics.Debug.WriteLine("ClearFilter: Filter cleared and view refreshed");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("ClearFilter: ItemsSource is null");
            }
        }

        private bool FilterItem(object value)
        {
            if (value == null)
            {
                System.Diagnostics.Debug.WriteLine("FilterItem: value is null");
                return false;
            }

            if (string.IsNullOrEmpty(currentFilter))
            {
                System.Diagnostics.Debug.WriteLine($"FilterItem: No filter, showing '{value}'");
                return true;
            }

            var itemText = value.ToString();
            var result = itemText.Contains(currentFilter, StringComparison.OrdinalIgnoreCase);
            System.Diagnostics.Debug.WriteLine($"FilterItem: '{itemText}' contains '{currentFilter}' = {result}");
            return result;
        }
    }
}