using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Printing;
using QRCoder;

namespace wpfprinterapp
{
    /// <summary>
    /// 智能标签打印系统主窗口
    /// </summary>
    public partial class MainWindow : Window
    {
        // 产品数据映射
        private readonly Dictionary<string, ProductInfo> _productMap = new()
        {
            { "e06 - 女式牛仔裤", new ProductInfo("e06", "女式牛仔裤", "件") },
            { "e10 - 男士T恤", new ProductInfo("e10", "男士T恤", "件") },
            { "b12 - 运动鞋", new ProductInfo("e12", "运动鞋", "双") },
            { "e15 - 西装套装", new ProductInfo("e15", "西装套装", "套") }
        };

        // 尺码数据
        private readonly Dictionary<string, string[]> _sizeOptions = new()
        {
            { "char", new[] { "S", "M", "L", "XL", "XXL", "XXXL" } },
            { "num", new[] { "110", "120", "130", "140", "150", "160", "170", "180" } }
        };

        // 颜色选项
        private readonly string[] _colorOptions = { "黑色", "白色", "牛仔蓝", "卡其色", "红色", "绿色", "灰色" };

        private string _lastValidProduct = "e06 - 女式牛仔裤";

        public MainWindow()
        {
            InitializeComponent();
            InitializeData();
            BindEvents();
            UpdatePreview();
        }

        /// <summary>
        /// 绑定事件处理器
        /// </summary>
        private void BindEvents()
        {
            // 绑定事件处理器
            ProductCodeComboBox.SelectionChanged += ProductCodeComboBox_SelectionChanged;
            ColorComboBox.SelectionChanged += ColorComboBox_SelectionChanged;
            SizeComboBox.SelectionChanged += SizeComboBox_SelectionChanged;
            QuantityTextBox.TextChanged += QuantityTextBox_TextChanged;
            CharSizeRadio.Checked += SizeTypeRadio_Checked;
            NumSizeRadio.Checked += SizeTypeRadio_Checked;
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化产品编号下拉框
                ProductCodeComboBox.ItemsSource = _productMap.Keys;
                ProductCodeComboBox.SelectedItem = _lastValidProduct;

                // 初始化颜色下拉框
                ColorComboBox.ItemsSource = _colorOptions;
                ColorComboBox.SelectedIndex = 0;

                // 初始化尺码类型
                CharSizeRadio.IsChecked = true;
                UpdateSizeOptions();

                // 初始化数量
                QuantityTextBox.Text = "100";

                // 初始化打印张数
                PrintCountTextBox.Text = "5";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化数据时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 更新尺码选项
        /// </summary>
        private void UpdateSizeOptions()
        {
            try
            {
                if (CharSizeRadio?.IsChecked == true)
                {
                    SizeComboBox.ItemsSource = _sizeOptions["char"];
                }
                else if (NumSizeRadio?.IsChecked == true)
                {
                    SizeComboBox.ItemsSource = _sizeOptions["num"];
                }

                // 设置默认选择
                if (SizeComboBox.Items.Count > 0)
                {
                    SizeComboBox.SelectedIndex = 0;
                }

                UpdatePreview();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新尺码选项时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新标签预览
        /// </summary>
        private void UpdatePreview()
        {
            try
            {
                // 检查所有必要的控件是否已初始化
                if (ProductCodeComboBox == null || ColorComboBox == null ||
                    SizeComboBox == null || QuantityTextBox == null ||
                    UnitTextBox == null || LabelSkuTextBlock == null ||
                    LabelQuantityTextBlock == null || QrDataTextBlock == null ||
                    QrCodeImage == null)
                {
                    return;
                }

                // 优先使用 SelectedItem，如果为空则使用 Text
                var productDisplayValue = ProductCodeComboBox.SelectedItem?.ToString() ?? ProductCodeComboBox.Text ?? "";
                if (string.IsNullOrEmpty(productDisplayValue) || !_productMap.ContainsKey(productDisplayValue))
                {
                    return; // 无效产品时不更新预览
                }

                var productInfo = _productMap[productDisplayValue];
                var color = ColorComboBox.SelectedItem?.ToString() ?? ColorComboBox.Text ?? "";
                var size = SizeComboBox.SelectedItem?.ToString() ?? "";
                var quantity = QuantityTextBox.Text ?? "0";

                // 更新单位
                UnitTextBox.Text = productInfo.Unit;

                // 更新SKU显示
                LabelSkuTextBlock.Text = $"{productInfo.Code}-{productInfo.Name}-{color}-{size}";

                // 更新数量显示
                LabelQuantityTextBlock.Text = $"{quantity} {productInfo.Unit}";

                // 更新二维码数据和图片
                var qrData = $"{productInfo.Code}-{color.ToLower().Replace(" ", "")}-{size}-{quantity}-{productInfo.Unit.ToLower()}";
                QrDataTextBlock.Text = qrData;

                // 生成二维码图片
                GenerateQrCode(qrData);
            }
            catch (Exception ex)
            {
                // 处理异常，可以记录日志或显示错误信息
                System.Diagnostics.Debug.WriteLine($"更新预览时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成二维码图片
        /// </summary>
        /// <param name="data">二维码数据</param>
        private void GenerateQrCode(string data)
        {
            try
            {
                using (var qrGenerator = new QRCodeGenerator())
                {
                    var qrCodeData = qrGenerator.CreateQrCode(data, QRCodeGenerator.ECCLevel.Q);
                    using (var qrCode = new QRCode(qrCodeData))
                    {
                        using (var qrCodeBitmap = qrCode.GetGraphic(20))
                        {
                            // 将Bitmap转换为BitmapImage
                            var bitmapImage = ConvertBitmapToBitmapImage(qrCodeBitmap);
                            QrCodeImage.Source = bitmapImage;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"生成二维码时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 将System.Drawing.Bitmap转换为WPF的BitmapImage
        /// </summary>
        /// <param name="bitmap">要转换的Bitmap</param>
        /// <returns>转换后的BitmapImage</returns>
        private BitmapImage ConvertBitmapToBitmapImage(Bitmap bitmap)
        {
            using (var memory = new MemoryStream())
            {
                bitmap.Save(memory, System.Drawing.Imaging.ImageFormat.Png);
                memory.Position = 0;

                var bitmapImage = new BitmapImage();
                bitmapImage.BeginInit();
                bitmapImage.StreamSource = memory;
                bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                bitmapImage.EndInit();
                bitmapImage.Freeze();

                return bitmapImage;
            }
        }

        // 事件处理器
        private void ProductCodeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ProductCodeComboBox.SelectedItem != null)
            {
                _lastValidProduct = ProductCodeComboBox.SelectedItem.ToString()!;
                UpdatePreview();
            }
        }

        private void ColorComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void SizeTypeRadio_Checked(object sender, RoutedEventArgs e)
        {
            UpdateSizeOptions();
        }

        private void SizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void QuantityTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证输入
                if (!ValidatePrintInputs())
                {
                    return;
                }

                var printCount = int.Parse(PrintCountTextBox.Text);
                var productDisplayValue = ProductCodeComboBox.SelectedItem?.ToString() ?? ProductCodeComboBox.Text ?? "";

                if (!_productMap.ContainsKey(productDisplayValue))
                {
                    MessageBox.Show("请选择有效的产品", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var productInfo = _productMap[productDisplayValue];
                var color = ColorComboBox.SelectedItem?.ToString() ?? ColorComboBox.Text ?? "";
                var size = SizeComboBox.SelectedItem?.ToString() ?? "";
                var quantity = QuantityTextBox.Text ?? "0";

                // 显示打印确认对话框
                var message = $"准备打印 {printCount} 张标签:\n" +
                             $"产品: {productInfo.Code} - {productInfo.Name}\n" +
                             $"颜色: {color}\n" +
                             $"尺码: {size}\n" +
                             $"数量: {quantity} {productInfo.Unit}";

                var result = MessageBox.Show(message, "打印确认", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // 执行打印
                    PrintLabels(printCount, productInfo, color, size, quantity);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打印时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 验证打印输入参数
        /// </summary>
        /// <returns>验证是否通过</returns>
        private bool ValidatePrintInputs()
        {
            // 验证打印张数
            if (!int.TryParse(PrintCountTextBox.Text, out int printCount) || printCount <= 0)
            {
                MessageBox.Show("请输入有效的打印张数（大于0的整数）", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                PrintCountTextBox.Focus();
                return false;
            }

            if (printCount > 100)
            {
                var result = MessageBox.Show($"您要打印 {printCount} 张标签，这是一个较大的数量。确定要继续吗？",
                    "打印确认", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result != MessageBoxResult.Yes)
                {
                    return false;
                }
            }

            // 验证产品选择
            var productDisplayValue = ProductCodeComboBox.SelectedItem?.ToString() ?? ProductCodeComboBox.Text ?? "";
            if (string.IsNullOrEmpty(productDisplayValue))
            {
                MessageBox.Show("请选择产品", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                ProductCodeComboBox.Focus();
                return false;
            }

            // 验证颜色选择
            var colorValue = ColorComboBox.SelectedItem?.ToString() ?? ColorComboBox.Text ?? "";
            if (string.IsNullOrEmpty(colorValue))
            {
                MessageBox.Show("请选择颜色", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                ColorComboBox.Focus();
                return false;
            }

            // 验证尺码选择
            if (SizeComboBox.SelectedItem == null)
            {
                MessageBox.Show("请选择尺码", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                SizeComboBox.Focus();
                return false;
            }

            // 验证数量输入
            if (!int.TryParse(QuantityTextBox.Text, out int quantity) || quantity <= 0)
            {
                MessageBox.Show("请输入有效的库存数量（大于0的整数）", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                QuantityTextBox.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// 执行标签打印
        /// </summary>
        /// <param name="printCount">打印张数</param>
        /// <param name="productInfo">产品信息</param>
        /// <param name="color">颜色</param>
        /// <param name="size">尺码</param>
        /// <param name="quantity">数量</param>
        private void PrintLabels(int printCount, ProductInfo productInfo, string color, string size, string quantity)
        {
            try
            {
                // 创建打印对话框
                var printDialog = new PrintDialog();

                // 显示打印对话框，让用户选择打印机
                if (printDialog.ShowDialog() == true)
                {
                    // 创建打印文档
                    var printDocument = CreatePrintDocument(printCount, productInfo, color, size, quantity);

                    // 执行打印
                    printDialog.PrintDocument(printDocument.DocumentPaginator, "标签打印");

                    MessageBox.Show($"已成功发送 {printCount} 张标签到打印机", "打印成功",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打印失败: {ex.Message}", "打印错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 创建打印文档
        /// </summary>
        /// <param name="printCount">打印张数</param>
        /// <param name="productInfo">产品信息</param>
        /// <param name="color">颜色</param>
        /// <param name="size">尺码</param>
        /// <param name="quantity">数量</param>
        /// <returns>打印文档</returns>
        private FixedDocument CreatePrintDocument(int printCount, ProductInfo productInfo, string color, string size, string quantity)
        {
            var document = new FixedDocument();

            // 设置页面大小（标签尺寸：80mm x 60mm，转换为设备无关单位）
            var pageSize = new System.Windows.Size(226.77, 170.08); // 80mm x 60mm in 96 DPI

            for (int i = 0; i < printCount; i++)
            {
                var page = CreateLabelPage(productInfo, color, size, quantity, pageSize);
                document.Pages.Add(page);
            }

            return document;
        }

        /// <summary>
        /// 创建单个标签页面
        /// </summary>
        /// <param name="productInfo">产品信息</param>
        /// <param name="color">颜色</param>
        /// <param name="size">尺码</param>
        /// <param name="quantity">数量</param>
        /// <param name="pageSize">页面尺寸</param>
        /// <returns>页面内容</returns>
        private PageContent CreateLabelPage(ProductInfo productInfo, string color, string size, string quantity, System.Windows.Size pageSize)
        {
            var pageContent = new PageContent();
            var fixedPage = new FixedPage
            {
                Width = pageSize.Width,
                Height = pageSize.Height,
                Background = System.Windows.Media.Brushes.White
            };

            // 创建标签内容
            var labelContent = CreateLabelContent(productInfo, color, size, quantity, pageSize);
            fixedPage.Children.Add(labelContent);

            pageContent.Child = fixedPage;
            return pageContent;
        }

        /// <summary>
        /// 创建标签内容
        /// </summary>
        /// <param name="productInfo">产品信息</param>
        /// <param name="color">颜色</param>
        /// <param name="size">尺码</param>
        /// <param name="quantity">数量</param>
        /// <param name="pageSize">页面尺寸</param>
        /// <returns>标签内容容器</returns>
        private Canvas CreateLabelContent(ProductInfo productInfo, string color, string size, string quantity, System.Windows.Size pageSize)
        {
            var canvas = new Canvas
            {
                Width = pageSize.Width,
                Height = pageSize.Height,
                Background = System.Windows.Media.Brushes.White
            };

            // 创建边框
            var border = new System.Windows.Shapes.Rectangle
            {
                Width = pageSize.Width - 4,
                Height = pageSize.Height - 4,
                Stroke = System.Windows.Media.Brushes.Black,
                StrokeThickness = 1,
                Fill = System.Windows.Media.Brushes.Transparent
            };
            Canvas.SetLeft(border, 2);
            Canvas.SetTop(border, 2);
            canvas.Children.Add(border);

            // SKU信息
            var skuText = $"{productInfo.Code}-{productInfo.Name}-{color}-{size}";
            var skuTextBlock = new TextBlock
            {
                Text = skuText,
                FontFamily = new System.Windows.Media.FontFamily("Consolas"),
                FontSize = 10,
                FontWeight = FontWeights.Bold,
                Foreground = System.Windows.Media.Brushes.Black,
                TextWrapping = TextWrapping.Wrap,
                Width = pageSize.Width - 20
            };
            Canvas.SetLeft(skuTextBlock, 10);
            Canvas.SetTop(skuTextBlock, 10);
            canvas.Children.Add(skuTextBlock);

            // 数量信息
            var quantityText = $"{quantity} {productInfo.Unit}";
            var quantityTextBlock = new TextBlock
            {
                Text = quantityText,
                FontFamily = new System.Windows.Media.FontFamily("Arial"),
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = System.Windows.Media.Brushes.Black,
                HorizontalAlignment = HorizontalAlignment.Center
            };
            Canvas.SetLeft(quantityTextBlock, (pageSize.Width - 100) / 2);
            Canvas.SetTop(quantityTextBlock, 35);
            canvas.Children.Add(quantityTextBlock);

            // 生成并添加二维码
            var qrData = $"{productInfo.Code}-{color.ToLower().Replace(" ", "")}-{size}-{quantity}-{productInfo.Unit.ToLower()}";
            var qrCodeImage = CreateQrCodeForPrint(qrData, 80); // 80x80像素的二维码
            if (qrCodeImage != null)
            {
                Canvas.SetLeft(qrCodeImage, (pageSize.Width - 80) / 2);
                Canvas.SetTop(qrCodeImage, 60);
                canvas.Children.Add(qrCodeImage);
            }

            // 添加二维码数据文本（小字体）
            var qrDataTextBlock = new TextBlock
            {
                Text = qrData,
                FontFamily = new System.Windows.Media.FontFamily("Consolas"),
                FontSize = 6,
                Foreground = System.Windows.Media.Brushes.Black,
                HorizontalAlignment = HorizontalAlignment.Center,
                Width = pageSize.Width - 10
            };
            Canvas.SetLeft(qrDataTextBlock, 5);
            Canvas.SetTop(qrDataTextBlock, 150);
            canvas.Children.Add(qrDataTextBlock);

            return canvas;
        }

        /// <summary>
        /// 为打印创建二维码图像
        /// </summary>
        /// <param name="data">二维码数据</param>
        /// <param name="size">图像尺寸</param>
        /// <returns>二维码图像控件</returns>
        private System.Windows.Controls.Image? CreateQrCodeForPrint(string data, int size)
        {
            try
            {
                using (var qrGenerator = new QRCodeGenerator())
                {
                    var qrCodeData = qrGenerator.CreateQrCode(data, QRCodeGenerator.ECCLevel.Q);
                    using (var qrCode = new QRCode(qrCodeData))
                    {
                        using (var qrCodeBitmap = qrCode.GetGraphic(20))
                        {
                            var bitmapImage = ConvertBitmapToBitmapImage(qrCodeBitmap);
                            return new System.Windows.Controls.Image
                            {
                                Source = bitmapImage,
                                Width = size,
                                Height = size,
                                Stretch = Stretch.Uniform
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建打印二维码时发生错误: {ex.Message}");
                return null;
            }
        }
    }

    /// <summary>
    /// 产品信息类
    /// </summary>
    public class ProductInfo
    {
        public string Code { get; }
        public string Name { get; }
        public string Unit { get; }

        public ProductInfo(string code, string name, string unit)
        {
            Code = code;
            Name = name;
            Unit = unit;
        }
    }
}
